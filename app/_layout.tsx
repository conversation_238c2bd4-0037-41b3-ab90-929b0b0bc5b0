import { useFonts } from "expo-font";
import { Slot } from "expo-router";
import { GestureHandlerRootView } from "react-native-gesture-handler";
import SuperTokens from "supertokens-react-native";
import { OverlayProvider } from "stream-chat-expo";
import type { DeepPartial, Theme } from "stream-chat-expo";
import { KeyboardAvoidingView, Platform } from "react-native";

import "react-native-reanimated";

import { useColorScheme } from "@/hooks/useColorScheme";
import { AppProvider } from "@/context/app";
import { QueryProvider } from "@/lib/providers/query-client";
import OfflineNotice from "@/components/OfflineNotice";

import * as Notifications from "expo-notifications";
import messaging from "@react-native-firebase/messaging";

if (Platform.OS !== "web") {
  messaging().setBackgroundMessageHandler(async (remoteMessage) => {
    console.log("Message handled in the background!", remoteMessage);

    // Ensure channel exists (Android)
    if (Platform.OS === "android") {
      await Notifications.setNotificationChannelAsync("default", {
        name: "default",
        importance: Notifications.AndroidImportance.MAX,
        vibrationPattern: [0, 250, 250, 250],
        lightColor: "#FF231F7C",
      });
    }

    // Extract notification data
    const notificationData = remoteMessage.data || {};
    const notificationContent = remoteMessage.notification;

    // Use expo-notifications to display the notification
    await Notifications.scheduleNotificationAsync({
      content: {
        title:
          notificationContent?.title ||
          notificationData?.title ||
          "New Message",
        body: notificationContent?.body || notificationData?.body,
        data: notificationData, // Pass along the data payload
      },
      trigger: null, // Show immediately
    });
  });
}

SuperTokens.init({
  apiDomain: "https://api.slosphere.com",
  apiBasePath: "/auth",
});

const theme: DeepPartial<Theme> = {
  channelListMessenger: {
    flatList: {
      backgroundColor: "black",
      padding: 0,
    },
    flatListContent: {
      backgroundColor: "black",
      padding: 0,
    },
  },
  channelListLoadingIndicator: {
    container: {
      backgroundColor: "#000",
    },
  },
  channelListFooterLoadingIndicator: {
    container: {
      backgroundColor: "#000",
    },
  },
  channelListHeaderErrorIndicator: {
    container: {
      backgroundColor: "#000",
    },
  },
  channelListSkeleton: {
    animationTime: 1800,
    background: {
      backgroundColor: "#000",
    },
    container: {
      backgroundColor: "#000",
    },
    gradientStart: {
      stopOpacity: 0,
    },
    gradientStop: {
      stopOpacity: 0.5,
    },
    height: 64,
    maskFillColor: "#333",
  },
  channelPreview: {
    container: {
      backgroundColor: "black",
      paddingHorizontal: 0,
    },
    avatar: {
      size: 50,
    },
    title: {
      color: "white",
      fontSize: 16,
      fontWeight: "bold",
    },
    message: {
      marginTop: 0,
      lineHeight: 12,
    },
  },
  groupAvatar: {
    container: {
      backgroundColor: "white",
      borderRadius: 0,
    },
    image: {
      borderRadius: 0,
    },
  },
  messageList: {
    container: {
      backgroundColor: "black",
    },
    inlineUnreadIndicator: {
      container: {
        backgroundColor: "transparent",
      },
      text: {
        color: "#EF5252",
      },
    },
    messageContainer: {
      backgroundColor: "#4a4a4a",
    },
    unreadMessagesNotification: {
      container: {
        backgroundColor: "transparent",
      },
    },
  },
  messageInput: {
    container: {
      backgroundColor: "black",
    },
    inputBox: {
      color: "white",
    },
    replyContainer: {
      marginBottom: 12,
    },
  },
  channel: {
    selectChannel: {
      backgroundColor: "black",
    },
  },

  reply: {
    messageContainer: {
      backgroundColor: "#1a1a1a",
    },
    markdownStyles: {
      text: {
        color: "#ffffff",
      },
    },
  },
  thread: {
    newThread: {
      backgroundColor: "black",
      backgroundGradientStart: "black",
      backgroundGradientStop: "black",
      text: {
        color: "#EF5252",
      },
    },
  },
  messageSimple: {
    content: {
      containerInner: {
        backgroundColor: "#1A1A1A",
        borderColor: "transparent",
      },
      replyContainer: {
        backgroundColor: "#4a4a4a",
        padding: 8,
      },
      replyBorder: {
        borderColor: "#4a4a4a",
        borderWidth: 1,
      },
      markdown: {
        text: {
          color: "white",
        },
        autolink: {
          color: "#005FFF",
        },
        codeBlock: {
          color: "#FFFFFF",
        },
        em: {
          color: "#FFFFFF",
        },
        strong: {
          color: "#FFFFFF",
        },
        paragraph: {
          color: "#FFFFFF",
        },
      },
      metaText: {
        color: "#72767E",
      },
      textContainer: {
        backgroundColor: "#1A1A1A",
        borderRadius: 12,
      },
      messageUser: {
        color: "#FFFFFF",
      },
    },
    status: {
      readByCount: {
        color: "#72767E",
      },
    },
    targetedMessageContainer: {
      backgroundColor: "#131313",
    },
    pinnedHeader: {
      label: {
        color: "white",
      },
    },
    reactionListTop: {
      container: {
        backgroundColor: "#1a1a1a",
        borderColor: "black",
      },
    },
    replies: {
      avatarSize: 14,
      container: {
        borderRadius: 20,
      },
      leftCurve: {
        borderColor: "#4a4a4a",
      },
      messageRepliesText: {
        padding: 4,
        color: "#4a4a4a",
      },
      rightCurve: {
        borderColor: "#4a4a4a",
      },
    },
    unreadUnderlayColor: "transparent",
  },
};

// Prevent the splash screen from auto-hiding before asset loading is complete.
// SplashScreen.preventAutoHideAsync();

export default function RootLayout() {
  const colorScheme = useColorScheme();
  const [loaded] = useFonts({
    SpaceMono: require("../assets/fonts/SpaceMono-Regular.ttf"),
  });

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <QueryProvider>
        <AppProvider>
          <OverlayProvider value={{ style: theme }}>
            <KeyboardAvoidingView
              style={{ flex: 1 }}
              behavior={Platform.OS === "ios" ? "padding" : "height"}
              keyboardVerticalOffset={Platform.OS === "ios" ? 0 : 0}
            >
              <Slot />
              <OfflineNotice />
            </KeyboardAvoidingView>
          </OverlayProvider>
        </AppProvider>
      </QueryProvider>
    </GestureHandlerRootView>
  );
}
